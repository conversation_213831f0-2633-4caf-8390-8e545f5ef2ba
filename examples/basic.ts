import { YellowstoneGeyserClient } from '../src/clients/yellowstone'

const client = new YellowstoneGeyserClient('https://solana-yellowstone-grpc.publicnode.com')

console.log('Version', await client.getVersion({}))

const stream = client.createStream({
    resubscribe: {
        shouldResubscribe: (error, status) => {
            console.log('Resubscribe check:', { error, status })

            return true
        },
    },
})

stream.on('subscribed', () => {
    console.log('Stream subscribed')
})

stream.on('error', (error) => {
    console.error('Stream error:', error)
})

stream.on('closed', () => {
    console.log('Stream closed')
})

stream.on('data', (data) => {
    console.log('Data received:', JSON.stringify(data))
})

stream.on('status', (status) => {
    console.log('Stream status:', status)
})

await stream.subscribe()

setTimeout(() => stream.close(false), 2000)
